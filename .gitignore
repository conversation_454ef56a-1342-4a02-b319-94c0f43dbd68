# ===================================
# UNIVERSAL GITIGNORE FOR FULL-STACK PROJECT
# ===================================

# ===================================
# NODE.JS & NPM
# ===================================
# Dependencies
node_modules/
*/node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Package manager lock files (keep package-lock.json but ignore others if needed)
# yarn.lock
# pnpm-lock.yaml

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# ===================================
# ENVIRONMENT VARIABLES & SECRETS
# ===================================
.env
.env.*
!.env.example
.env.local
.env.development.local
.env.test.local
.env.production.local

# API keys and secrets
config/secrets.js
secrets.json

# ===================================
# BUILD OUTPUTS & DIST
# ===================================
dist/
build/
*/dist/
*/build/
out/
.next/
.nuxt/
.vuepress/dist

# ===================================
# REACT & VITE SPECIFIC
# ===================================
# Vite
.vite/
vite.config.js.timestamp-*

# React build
/build
/public/build

# ===================================
# DATABASE & LOGS
# ===================================
# Database files
*.db
*.sqlite
*.sqlite3
data/
uploads/

# Log files
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# ===================================
# OPERATING SYSTEM
# ===================================
# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ===================================
# IDE & EDITORS
# ===================================
# VSCode
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

# IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~
.netrwhist

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ===================================
# TEMPORARY & CACHE FILES
# ===================================
# Temporary folders
tmp/
temp/
.tmp/
.temp/

# Cache
.cache/
.parcel-cache/
.eslintcache
.stylelintcache

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# ===================================
# TESTING
# ===================================
# Jest
coverage/
.nyc_output/

# Cypress
cypress/videos/
cypress/screenshots/

# ===================================
# DEPLOYMENT & CLOUD
# ===================================
# Vercel
.vercel

# Netlify
.netlify/

# Heroku
.heroku/

# AWS
.aws/

# Docker
.dockerignore

# ===================================
# MISCELLANEOUS
# ===================================
# Backup files
*.bak
*.backup
*.old

# Archive files
*.zip
*.tar.gz
*.rar

# Certificate files
*.pem
*.key
*.crt

# Local development
.local/

# ===================================
# PROJECT SPECIFIC
# ===================================
# Mental health app specific
/server/uploads/
/server/logs/
/client/dist/
/client/build/
